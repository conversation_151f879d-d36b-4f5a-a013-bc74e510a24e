# https://natapp.cn/ - 支持免费使用，但付费会更稳定一些
# 将本文件放置于natapp同级目录 程序将读取 [default] 段
# 在命令行参数模式如 natapp -authtoken=xxx 等相同参数将会覆盖掉此配置
# 命令行参数 -config= 可以指定任意config.ini文件
[default]
authtoken=ab0dcc1354bdcd61    #对应一条隧道的authtoken，你需要更换为你的。否则不能正常启动。
clienttoken=                    #对应客户端的clienttoken,将会忽略authtoken,若无请留空,
log=none                        #log 日志文件,可指定本地文件, none=不做记录,stdout=直接屏幕输出 ,默认为none
loglevel=ERROR                  #日志等级 DEBUG, INFO, WARNING, ERROR 默认为 DEBUG
http_proxy=                     #代理设置 如 http://10.123.10.10:3128 非代理上网用户请务必留空