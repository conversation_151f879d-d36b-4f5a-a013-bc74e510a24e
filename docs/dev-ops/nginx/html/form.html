<form name="punchout_form" method="post" action="https://openapi-sandbox.dl.alipaydev.com/gateway.do?charset=utf-8&method=alipay.trade.page.pay&sign=Mnlcrp0M3%2FfhKiqhrDvTP3DYgLg8jnwosv0qTCxO5ceKT8zj3HHKfBYVOic6C1EvfGuKCky97Zxukf8NA0lTORSNydFpb6yV6J1oo9PnBpVcNpVzegMhm0V6tBziIDwkDfXkOcL6cZmN93lgCrAVu5wYpb2VrOKDb0HtFYaGKXEgNV1kiS2251qyOIaQ9rK3GgyswXPa8pLwfEFx6tNA%2BkIbwEu%2BYiX0e459VOendGLmDPtXYP8DLtrCPQxje2xgnLB61cwroXa5qhRurLeMGZ3d8HWNRBLgtBnKbRviOlsO0diBMk%2ByMj1KrFreozOMJ%2FsTxjvNm8U0H%2F3x6NDERg%3D%3D&return_url=https%3A%2F%2Fgithub.com%2FWorld-controller&notify_url=http%3A%2F%2Fo0-goandrun.natapp1.cc%2Fapi%2Fv1%2Falipay%2Falipay_notify_url&version=1.0&app_id=9021000150649259&sign_type=RSA2&timestamp=2025-08-05+15%3A02%3A54&alipay_sdk=alipay-sdk-java-4.38.157.ALL&format=json">
    <input type="hidden" name="biz_content" value="{&quot;out_trade_no&quot;:&quot;9900519879783196&quot;,&quot;total_amount&quot;:&quot;1.68&quot;,&quot;subject&quot;:&quot;测试商品&quot;,&quot;product_code&quot;:&quot;FAST_INSTANT_TRADE_PAY&quot;}">
    <input type="submit" value="立即支付" style="display:none" >
</form>
<script>document.forms[0].submit();</script>