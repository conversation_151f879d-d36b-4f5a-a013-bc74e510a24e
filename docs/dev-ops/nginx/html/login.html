<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>S Pay Mall 商城登录页</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
        }
        .login-container {
            background-color: #fff;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 400px;
            width: 100%;
        }
        .login-container h1 {
            margin-bottom: 20px;
            color: #333;
        }
        .qr-code {
            margin: 20px 0;
        }
        .qr-code img {
            width: 200px;
            height: 200px;
        }
        .instructions {
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
<div class="login-container">
    <h1>S Pay Mall 商城登录页</h1>
    <div class="qr-code">
        <img id="qr-code-img" src="images/placeholder.png" alt="微信二维码">
    </div>
    <p class="instructions">请使用微信扫描二维码登录</p>
</div>

<script>
    document.addEventListener("DOMContentLoaded", function() {
        // 获取二维码 ticket
        fetch('http://localhost:8080/api/v1/login/weixin_qrcode_ticket')
            .then(response => response.json())
            .then(data => {
                if (data.code === "0000") {
                    const ticket = data.data;
                    const qrCodeImg = document.getElementById('qr-code-img');
                    qrCodeImg.src = `https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=${ticket}`;

                    // 开始轮询检查登录状态
                    const intervalId = setInterval(() => {
                        checkLoginStatus(ticket, intervalId);
                    }, 3000); // 每3秒检查一次
                } else {
                    console.error('获取二维码 ticket 失败:', data.info);
                }
            })
            .catch(error => {
                console.error('请求失败:', error);
            });

        function checkLoginStatus(ticket, intervalId) {
            fetch(`http://localhost:8080/api/v1/login/check_login?ticket=${ticket}`)
                .then(response => response.json())
                .then(data => {
                    if (data.code === "0000") {
                        console.info("login success");
                        // 停止轮询
                        clearInterval(intervalId);
                        // 保存登录 token 到 cookie，设置有效期为30天
                        setCookie('loginToken', data.data, 30);
                        // 在这里可以重定向到登录后的页面
                        window.location.href = 'index.html'; // 假设登录成功后跳转到首页
                    } else {
                        console.info("login wait");
                    }
                })
                .catch(error => {
                    console.error('请求失败:', error);
                });
        }

        function setCookie(name, value, days) {
            const date = new Date();
            date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
            const expires = "expires=" + date.toUTCString();
            document.cookie = name + "=" + value + ";" + expires + ";path=/";
        }
    });
</script>
</body>
</html>
