25-08-06.23:35:17.414 [main            ] INFO  Application            - Starting Application v1.0-SNAPSHOT using Java 1.8.0_342 on 781b3b29bfeb with PID 7 (/s-pay-mall-ddd-app.jar started by root in /)
25-08-06.23:35:17.418 [main            ] INFO  Application            - The following 1 profile is active: "prod"
25-08-06.23:35:18.790 [main            ] INFO  TomcatWebServer        - Tomcat initialized with port(s): 8080 (http)
25-08-06.23:35:18.801 [main            ] INFO  Http11NioProtocol      - Initializing ProtocolHandler ["http-nio-8080"]
25-08-06.23:35:18.802 [main            ] INFO  StandardService        - Starting service [Tomcat]
25-08-06.23:35:18.802 [main            ] INFO  StandardEngine         - Starting Servlet engine: [Apache Tomcat/9.0.75]
25-08-06.23:35:18.890 [main            ] INFO  [/]                    - Initializing Spring embedded WebApplicationContext
25-08-06.23:35:18.891 [main            ] INFO  ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1406 ms
25-08-06.23:35:20.316 [main            ] INFO  Http11NioProtocol      - Starting ProtocolHandler ["http-nio-8080"]
25-08-06.23:35:20.345 [main            ] INFO  TomcatWebServer        - Tomcat started on port(s): 8080 (http) with context path ''
25-08-06.23:35:20.361 [main            ] INFO  Application            - Started Application in 4.257 seconds (JVM running for 5.975)
25-08-06.23:35:21.000 [scheduling-1    ] INFO  NoPayNotifyOrderJob    - 任务：检测未接收到或未正确处理的支付回调通知
25-08-06.23:35:21.036 [scheduling-1    ] INFO  HikariDataSource       - HikariPool-1 - Starting...
25-08-06.23:35:21.351 [scheduling-1    ] INFO  HikariDataSource       - HikariPool-1 - Start completed.
25-08-06.23:35:24.000 [scheduling-1    ] INFO  NoPayNotifyOrderJob    - 任务：检测未接收到或未正确处理的支付回调通知
25-08-06.23:35:27.000 [scheduling-1    ] INFO  NoPayNotifyOrderJob    - 任务：检测未接收到或未正确处理的支付回调通知
