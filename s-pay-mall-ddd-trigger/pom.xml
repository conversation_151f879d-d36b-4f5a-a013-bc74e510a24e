<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>xyz.controller</groupId>
        <artifactId>s-pay-mall-ddd</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>s-pay-mall-ddd-trigger</artifactId>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

        <!-- 系统模块 -->
        <dependency>
            <groupId>xyz.controller</groupId>
            <artifactId>s-pay-mall-ddd-types</artifactId>
        </dependency>
        <dependency>
            <groupId>xyz.controller</groupId>
            <artifactId>s-pay-mall-ddd-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>xyz.controller</groupId>
            <artifactId>s-pay-mall-ddd-api</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

    <build>
        <finalName>s-pay-mall-ddd-trigger</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-archetype-plugin</artifactId>
                <version>3.2.0</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>create-from-project</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
