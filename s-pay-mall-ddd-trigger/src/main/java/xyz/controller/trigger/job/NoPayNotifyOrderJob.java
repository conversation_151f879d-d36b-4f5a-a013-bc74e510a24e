package xyz.controller.trigger.job;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.domain.AlipayTradeQueryModel;
import com.alipay.api.request.AlipayTradeQueryRequest;
import com.alipay.api.response.AlipayTradeQueryResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import xyz.controller.domain.order.service.IOrderService;

import javax.annotation.Resource;
import java.util.List;

/**
 * 对比项	               超时关闭任务                    支付回调补偿任务
 * 执行频率	每10分钟	                                     每3秒
 * 处理对象	超时30分钟的PAY_WAIT订单	    超时1分钟的PAY_WAIT订单
 * 处理数量	最多50个	                                最多10个
 * 处理逻辑	直接关闭订单	                            查询支付宝确认状态
 * 目标状态	CLOSE	                                    PAY_SUCCESS
 * 业务目的	释放资源，清理超时订单	            补偿丢失的支付通知
 */

/**
 * <AUTHOR> https://github.com/World-controller @GoAndRun
 * @description 补偿机制1：定时任务主动查询zfb对订单的处理信息
 * 业务场景：用户已经支付成功，zfb设置订单状态为“success”，对应状态码为“10000”但我们的系统没有收到支付宝的异步回调通知
 * 查询对象：查找状态为PAY_WAIT且创建时间超过1分钟的订单，这些订单可能存在回调丢失的情况。
 * @create 2025/7/26 14:19
 */
@Slf4j
@Component
public class NoPayNotifyOrderJob {

    @Resource
    private IOrderService orderService;

    @Resource
    private AlipayClient alipayClient;

    @Scheduled(cron = "0/3 * * * * ?")// 每3秒执行一次
    public void exec()  {

        try {
            log.info("任务：检测未接收到或未正确处理的支付回调通知");
            // 1. 查询可能丢失通知的订单
            List<String> orderIds = orderService.queryNoPayNotifyOrder();
            if (orderIds == null || orderIds.isEmpty()) return;
            //// 2. 主动查询支付宝支付状态
            for (String orderId : orderIds) {
                AlipayTradeQueryRequest request = new AlipayTradeQueryRequest();
                AlipayTradeQueryModel bizModel = new AlipayTradeQueryModel();
                bizModel.setOutTradeNo(orderId);
                request.setBizModel(bizModel);
                // 3. 调用支付宝查询接口
                AlipayTradeQueryResponse alipayTradeQueryResponse = alipayClient.execute(request);
                String code = alipayTradeQueryResponse.getCode();
                // 4. 如果支付成功，更新订单状态
                if ("10000".equals(code)){
                    orderService.changeOrderPaySuccess(orderId);
                }
            }
        } catch (AlipayApiException e) {
            log.error("检测未接收到或未正确处理的支付回调通知失败",e);
        }
    }


}
