package xyz.controller.trigger.job;

import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import xyz.controller.domain.order.service.IOrderService;

import javax.annotation.Resource;
import java.util.List;

/**
 * 对比项	               超时关闭任务                    支付回调补偿任务
 * 执行频率	每10分钟	                                     每3秒
 * 处理对象	超时30分钟的PAY_WAIT订单	    超时1分钟的PAY_WAIT订单
 * 处理数量	最多50个	                                最多10个
 * 处理逻辑	直接关闭订单	                            查询支付宝确认状态
 * 目标状态	CLOSE	                                    PAY_SUCCESS
 * 业务目的	释放资源，清理超时订单	            补偿丢失的支付通知
 */

/**
 * <AUTHOR> https://github.com/World-controller @GoAndRun
 * @description 超时关单
 * @create 2025/7/26 14:19
 */
@Slf4j
@Component
public class TimeoutCloseOrderJob {

    @Resource
    private IOrderService orderService;

    @Scheduled(cron = "0 0/10 * * * ?")//每10分钟执行一次
    public void exec() {
        try {
            log.info("任务；超时30分钟订单关闭");
            // 1. 查询需要关闭的超时订单
            List<String> orderIds = orderService.queryTimeoutCloseOrderList();
            // 2. 检查是否有需要处理的订单
            if (null == orderIds || orderIds.isEmpty()) {
                log.info("定时任务，超时30分钟订单关闭，暂无超时未支付订单 orderIds is null");
                return;
            }
            // 3. 逐个关闭超时订单
            for (String orderId : orderIds) {
                boolean status = orderService.changeOrderClose(orderId);
                log.info("定时任务，超时30分钟订单关闭 orderId: {} status：{}", orderId, status);
            }
        } catch (Exception e) {
            log.error("定时任务，超时15分钟订单关闭失败", e);
        }
    }
}
