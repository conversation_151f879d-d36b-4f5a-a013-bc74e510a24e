package xyz.controller.trigger.http;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import xyz.controller.api.IAuthService;
import xyz.controller.api.response.Response;
import xyz.controller.domain.auth.service.ILoginService;
import xyz.controller.types.common.Constants;

import javax.annotation.Resource;

/**
 * <AUTHOR> https://github.com/World-controller @GoAndRun
 * @description
 * @create 2025/8/3 8:10
 */
@Slf4j
@RestController
@CrossOrigin("*")
@RequestMapping("/api/v1/login/")
public class LoginController implements IAuthService {

    @Resource
    private ILoginService loginService;

    /**
     * http://o0.goandrun.natapp1.cc/api/v1/login/weixin_qrcode_ticket
     * weixinQrCodeTicket() 生成微信二维码票据，为用户提供扫码入口
     * @return 微信扫码登录所需的二维码票据（ticket）
     * code: 响应状态码（"0000"=成功，"0001"=失败）
     * info: 响应描述信息
     * data: 微信二维码票据字符串，用于生成二维码
     */
    @GetMapping("weixin_qrcode_ticket")
    @Override
    public Response<String> weixinQrCodeTicket() {
        try {
//            1.调用 loginService.createQrCodeTicket() 生成票据
            String qrCodeTicket = loginService.createQrCodeTicket();
            log.info("生成微信扫码登录 ticket:{}", qrCodeTicket);
//            2.构建成功响应，将票据作为 data 返回,异常时返回错误响应
            return Response.<String>builder()
                    .code(Constants.ResponseCode.SUCCESS.getCode())
                    .info(Constants.ResponseCode.SUCCESS.getInfo())
                    .data(qrCodeTicket)
                    .build();
        } catch (Exception e) {
            log.error("生成微信扫码登录 ticket 失败", e);
            return Response.<String>builder()
                    .code(Constants.ResponseCode.UN_ERROR.getCode())
                    .info(Constants.ResponseCode.UN_ERROR.getInfo())
                    .build();
        }
    }
    /**
     * checkLogin() - 检查指定票据对应的登录状态，用于前端轮询检测用户是否已完成扫码登录
     * http://o0.goandrun.natapp1.cc/api/v1/login/check_login
     * @ticket: 微信二维码票据，由 weixinQrCodeTicket() 方法生成，用作登录状态的唯一标识符
     * @return data: 用户的 openid 标识符，作为登录凭证
     * 登录成功时：{
     *   "code": "0000",
     *   "info": "调用成功",
     *   "data": "user_openid_12345"
     * }
     */
    @GetMapping("check_login")
    @Override
    public Response<String> checkLogin(String ticket) {
        try {
//            1.调用 loginService.checkLogin(ticket) 从缓存中查询登录状态
            String openidToken = loginService.checkLogin(ticket);
            log.info("扫码检测登录结果 ticket:{} openidToken:{}", ticket, openidToken);
//            2.如果找到 openidToken，返回登录成功响应
            if (StringUtils.isNotBlank(openidToken)) {
                return Response.<String>builder()
                        .code(Constants.ResponseCode.SUCCESS.getCode())
                        .info(Constants.ResponseCode.SUCCESS.getInfo())
                        .data(openidToken)
                        .build();
//                如果未找到，返回未登录响应
            } else {

                return Response.<String>builder()
                        .code(Constants.ResponseCode.NO_LOGIN.getCode())
                        .info(Constants.ResponseCode.NO_LOGIN.getInfo())
                        .build();
            }
//           3.异常时返回错误响应
        } catch (Exception e) {
            log.error("扫码检测登录结果失败 ticket:{}", ticket, e);
            return Response.<String>builder()
                    .code(Constants.ResponseCode.UN_ERROR.getCode())
                    .info(Constants.ResponseCode.UN_ERROR.getInfo())
                    .build();
        }
    }
}
