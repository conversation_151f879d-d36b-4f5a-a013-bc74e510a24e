package xyz.controller.trigger.listener;

import com.google.common.eventbus.Subscribe;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> https://github.com/World-controller @GoAndRun
 * @description 支付成功回调消息
 * @create 2025/7/26 14:19
 */
@Slf4j
@Component
public class OrderPaySuccessListener {
    //对应方法二：changeOrderPaySuccess() 的eventBus.post(JSON.toJSONString(payOrderReq));语句
    @Subscribe
    public void handleEvent(String paySuccessMessage){
        log.info("收到支付成功消息，可以做接下来的事情，如；发货、充值、开户员、返利 {}", paySuccessMessage);
    }

}
