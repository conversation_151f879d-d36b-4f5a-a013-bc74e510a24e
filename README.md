# xfg-frame-archetype - DDD 脚手架

# s-pay-mall-ddd 架构文档

## DDD依赖关系总结

- s-pay-mall-ddd-app: 依赖 application, trigger, infrastructure。这是应用的启动模块，依赖其他所有层是合理的。
- s-pay-mall-ddd-trigger: 依赖 application, types, api。trigger层作为入口，依赖application来处理业务逻辑，依赖api来定义接口，依赖types来使用通用类型，这是合理的。
- s-pay-mall-ddd-application: 依赖 domain。application层编排领域服务，所以依赖domain层是正确的。
- s-pay-mall-ddd-infrastructure: 依赖 domain。infrastructure层是domain层接口的实现，所以依赖domain是正确的。
- s-pay-mall-ddd-domain: 依赖 types。domain层只依赖了types，没有依赖任何其他层，这非常符合DDD的要求。领域层应该是独立的，不依赖于外部实现。
- s-pay-mall-ddd-api: 无项目内依赖。api模块定义了暴露给外部的接口，它本身不应该依赖项目内的其他模块。
- s-pay-mall-ddd-types: 无项目内依赖。types模块定义了通用的数据结构和枚举，它也不应该依赖项目内的其他模块。
- 依赖关系是单向的：app -> trigger -> application -> domain，以及 infrastructure -> domain。domain层处于中心位置，不依赖任何其他层（除了types）



## DDD架构图
```mermaid
graph TB
    %% 定义样式
    classDef apiLayer fill:#e1f5fe,stroke:#01579b,stroke-width:2px,color:#000
    classDef appLayer fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000
    classDef triggerLayer fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px,color:#000
    classDef domainLayer fill:#fff3e0,stroke:#e65100,stroke-width:3px,color:#000
    classDef infraLayer fill:#fce4ec,stroke:#880e4f,stroke-width:2px,color:#000
    classDef typesLayer fill:#f1f8e9,stroke:#33691e,stroke-width:2px,color:#000
    
    %% API层
    subgraph API["🔌 API层 (接口定义)"]
        IAuthService["IAuthService<br/>接口契约定义"]
        Response["Response&lt;T&gt;<br/>统一响应格式"]
        DTOs["DTO包<br/>数据传输对象"]
    end
    
    %% APP层
    subgraph APP["🚀 APP层 (应用启动)"]
        Application["Application<br/>Spring Boot启动类"]
        Config["Retrofit2Config<br/>配置管理"]
    end
    
    %% TRIGGER层
    subgraph TRIGGER["🎯 TRIGGER层 (触发器)"]
        LoginController["LoginController<br/>HTTP接口处理"]
        JobTrigger["Job包<br/>定时任务触发"]
        ListenerTrigger["Listener包<br/>消息监听触发"]
    end
    
    %% DOMAIN层 (核心)
    subgraph DOMAIN["💎 DOMAIN层 (领域核心)"]
        subgraph DomainService["领域服务"]
            ILoginService["ILoginService<br/>业务接口"]
            WeixinLoginService["WeixinLoginService<br/>微信登录业务逻辑"]
        end
        
        subgraph DomainModel["领域模型"]
            Aggregate["聚合对象<br/>XxxAggregate"]
            Entity["实体对象<br/>XxxEntity"]
            ValueObject["值对象<br/>XxxVO"]
        end
        
        subgraph DomainAdapter["适配器接口"]
            ILoginPort["ILoginPort<br/>外部系统接口"]
            IRepository["IRepository<br/>仓储接口"]
        end
    end
    
    %% INFRASTRUCTURE层
    subgraph INFRA["🏗️ INFRASTRUCTURE层 (基础设施)"]
        subgraph AdapterImpl["适配器实现"]
            LoginPort["LoginPort<br/>外部系统适配器"]
            RepositoryImpl["Repository实现<br/>数据访问适配器"]
        end
        
        subgraph Gateway["网关层"]
            IWeixinApiService["IWeixinApiService<br/>微信API网关"]
            WeixinDTOs["微信DTO对象<br/>复杂数据结构"]
        end
        
        subgraph Persistent["持久化层"]
            DAO["DAO接口<br/>数据访问"]
            PO["PO对象<br/>持久化对象"]
        end
    end
    
    %% TYPES层
    subgraph TYPES["🔧 TYPES层 (通用类型)"]
        Constants["Constants<br/>常量定义"]
        AppException["AppException<br/>异常定义"]
        Utils["工具类<br/>XmlUtil, SignatureUtil"]
    end
    
    %% 依赖关系 (依赖倒置体现)
    APP --> TRIGGER
    APP --> INFRA
    TRIGGER --> DOMAIN
    TRIGGER --> API
    INFRA --> DOMAIN
    DOMAIN --> TYPES
    INFRA --> TYPES
    TRIGGER --> TYPES
    
    %% 具体调用链路
    LoginController -.->|实现| IAuthService
    LoginController -.->|调用| ILoginService
    WeixinLoginService -.->|实现| ILoginService
    WeixinLoginService -.->|依赖| ILoginPort
    LoginPort -.->|实现| ILoginPort
    LoginPort -.->|调用| IWeixinApiService
    
    %% 应用样式
    class API apiLayer
    class APP appLayer
    class TRIGGER triggerLayer
    class DOMAIN domainLayer
    class INFRA infraLayer
    class TYPES typesLayer
    
    %% 添加说明
    note1["🔄 依赖倒置原则:<br/>Infrastructure层依赖Domain层<br/>而不是相反"]
    note2["🎭 适配器模式:<br/>LoginPort适配微信API<br/>隔离外部系统复杂性"]
    note3["📦 关注点分离:<br/>每层职责明确<br/>业务逻辑与技术实现分离"]
```
## MVC vs DDD对比图
```mermaid
graph TB
    %% 定义样式
    classDef mvcController fill:#ffcdd2,stroke:#c62828,stroke-width:2px,color:#000
    classDef mvcService fill:#f8bbd9,stroke:#ad1457,stroke-width:2px,color:#000
    classDef mvcDao fill:#e1bee7,stroke:#6a1b9a,stroke-width:2px,color:#000
    classDef dddApi fill:#e1f5fe,stroke:#01579b,stroke-width:2px,color:#000
    classDef dddTrigger fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px,color:#000
    classDef dddDomain fill:#fff3e0,stroke:#e65100,stroke-width:3px,color:#000
    classDef dddInfra fill:#fce4ec,stroke:#880e4f,stroke-width:2px,color:#000
    
    subgraph MVC["🏛️ 传统MVC架构"]
        subgraph MVCController["Controller层"]
            PaymentController["PaymentController<br/>• 处理HTTP请求<br/>• 参数验证<br/>• 响应格式化<br/>• 部分业务逻辑"]
        end
        
        subgraph MVCService["Service层"]
            PaymentService["PaymentService<br/>• 核心业务逻辑<br/>• 事务管理<br/>• 业务规则验证<br/>• 调用DAO层"]
        end
        
        subgraph MVCDao["DAO层"]
            PaymentDAO["PaymentDAO<br/>• 数据库操作<br/>• SQL执行<br/>• 数据映射<br/>• 外部API调用"]
        end
        
        Database1[("💾 数据库")]
        ExternalAPI1[("🌐 外部API")]
    end
    
    subgraph DDD["🏗️ DDD架构"]
        subgraph DDDApi["API层"]
            IPaymentService["IPaymentService<br/>• 接口契约<br/>• DTO定义"]
        end
        
        subgraph DDDTrigger["Trigger层"]
            PaymentControllerDDD["PaymentController<br/>• HTTP处理<br/>• 请求转换"]
        end
        
        subgraph DDDDomain["Domain层 (核心)"]
            PaymentServiceDDD["PaymentService<br/>• 纯业务逻辑<br/>• 领域规则"]
            IPaymentPort["IPaymentPort<br/>• 外部接口定义"]
            IPaymentRepo["IPaymentRepository<br/>• 仓储接口定义"]
        end
        
        subgraph DDDInfra["Infrastructure层"]
            PaymentPort["PaymentPort<br/>• 外部API适配"]
            PaymentRepo["PaymentRepository<br/>• 数据访问实现"]
        end
        
        Database2[("💾 数据库")]
        ExternalAPI2[("🌐 外部API")]
    end
    
    %% MVC依赖关系 (传统方向)
    PaymentController -->|直接依赖| PaymentService
    PaymentService -->|直接依赖| PaymentDAO
    PaymentDAO --> Database1
    PaymentDAO --> ExternalAPI1
    
    %% DDD依赖关系 (依赖倒置)
    PaymentControllerDDD -->|实现| IPaymentService
    PaymentControllerDDD -->|调用| PaymentServiceDDD
    PaymentServiceDDD -->|依赖抽象| IPaymentPort
    PaymentServiceDDD -->|依赖抽象| IPaymentRepo
    PaymentPort -->|实现| IPaymentPort
    PaymentRepo -->|实现| IPaymentRepo
    PaymentPort --> ExternalAPI2
    PaymentRepo --> Database2
    
    %% 应用样式
    class PaymentController mvcController
    class PaymentService mvcService
    class PaymentDAO mvcDao
    class IPaymentService dddApi
    class PaymentControllerDDD dddTrigger
    class PaymentServiceDDD,IPaymentPort,IPaymentRepo dddDomain
    class PaymentPort,PaymentRepo dddInfra
    
    %% 对比说明
    subgraph Comparison["📊 架构对比"]
        direction TB
        CompTable["
        | 对比维度 | 传统MVC | DDD架构 |
        |---------|---------|---------|
        | 层次数量 | 3层 | 6模块 |
        | 依赖方向 | 上层→下层 | 低层→高层 |
        | 业务逻辑 | 分散在多层 | 集中在Domain |
        | 外部依赖 | 直接耦合 | 接口隔离 |
        | 可测试性 | 需要完整环境 | 易于单元测试 |
        | 可扩展性 | 修改影响多层 | 适配器模式 |
        "]
    end
```
## MVC->DDD重构路线图
```mermaid
graph TD
    %% 定义样式
    classDef phaseBox fill:#e3f2fd,stroke:#1976d2,stroke-width:2px,color:#000
    classDef actionBox fill:#f3e5f5,stroke:#7b1fa2,stroke-width:1px,color:#000
    classDef resultBox fill:#e8f5e8,stroke:#388e3c,stroke-width:1px,color:#000
    classDef challengeBox fill:#fff3e0,stroke:#f57c00,stroke-width:1px,color:#000
    
    Start([🚀 开始重构]) --> Phase1
    
    subgraph Phase1["📦 第一阶段: 模块拆分"]
        P1A["创建6个Maven模块<br/>• s-pay-mall-ddd-api<br/>• s-pay-mall-ddd-app<br/>• s-pay-mall-ddd-domain<br/>• s-pay-mall-ddd-trigger<br/>• s-pay-mall-ddd-infrastructure<br/>• s-pay-mall-ddd-types"]
        P1B["建立依赖关系<br/>• app → trigger + infrastructure<br/>• trigger → domain + api<br/>• infrastructure → domain<br/>• domain → types"]
        P1C["迁移通用类型<br/>• 常量 → types<br/>• 异常 → types<br/>• 工具类 → types"]
    end
    
    Phase1 --> Phase2
    
    subgraph Phase2["🔌 第二阶段: 接口层重构"]
        P2A["提取Controller接口<br/>• PaymentController → IPaymentService<br/>• 定义方法签名<br/>• 移除实现细节"]
        P2B["创建统一响应格式<br/>• Response&lt;T&gt; 泛型类<br/>• 标准化返回结构<br/>• 错误码统一管理"]
        P2C["定义DTO对象<br/>• RequestDTO<br/>• ResponseDTO<br/>• 数据验证注解"]
    end
    
    Phase2 --> Phase3
    
    subgraph Phase3["💎 第三阶段: 领域层建模"]
        P3A["识别业务领域<br/>• 支付领域<br/>• 用户领域<br/>• 订单领域"]
        P3B["提取聚合和实体<br/>• PaymentAggregate<br/>• PaymentEntity<br/>• AmountValueObject"]
        P3C["定义适配器接口<br/>• IPaymentPort<br/>• IPaymentRepository<br/>• 依赖倒置设计"]
        P3D["迁移业务逻辑<br/>• Service → Domain Service<br/>• 纯业务逻辑<br/>• 去除技术依赖"]
    end
    
    Phase3 --> Phase4
    
    subgraph Phase4["🏗️ 第四阶段: 基础设施层实现"]
        P4A["实现适配器接口<br/>• PaymentPort implements IPaymentPort<br/>• 处理外部API调用<br/>• 数据格式转换"]
        P4B["创建Gateway层<br/>• IWeixinApiService<br/>• 复杂DTO对象<br/>• HTTP客户端配置"]
        P4C["重构数据访问<br/>• DAO → Repository<br/>• PO对象设计<br/>• 数据库操作封装"]
    end
    
    Phase4 --> Phase5
    
    subgraph Phase5["🎯 第五阶段: 触发器层重构"]
        P5A["重构Controller<br/>• 实现API接口<br/>• 简化为请求处理<br/>• 移除业务逻辑"]
        P5B["添加多种触发方式<br/>• HTTP触发器<br/>• 消息监听器<br/>• 定时任务"]
        P5C["请求响应转换<br/>• DTO转换<br/>• 异常处理<br/>• 日志记录"]
    end
    
    Phase5 --> Phase6
    
    subgraph Phase6["🚀 第六阶段: 应用层整合"]
        P6A["配置Spring Boot<br/>• Application启动类<br/>• 组件扫描配置<br/>• 自动装配"]
        P6B["整合所有模块<br/>• 依赖注入<br/>• Bean配置<br/>• 配置文件管理"]
        P6C["测试和验证<br/>• 单元测试<br/>• 集成测试<br/>• 功能验证"]
    end
    
    Phase6 --> Complete([✅ 重构完成])
    
    %% 挑战和解决方案
    subgraph Challenges["⚠️ 重构挑战与解决方案"]
        C1["业务逻辑分散<br/>→ 重新组织到聚合"]
        C2["依赖关系复杂<br/>→ 接口定义明确依赖"]
        C3["测试复杂度增加<br/>→ 依赖注入简化测试"]
        C4["团队学习成本<br/>→ 培训和文档更新"]
    end
    
    %% 应用样式
    class Phase1,Phase2,Phase3,Phase4,Phase5,Phase6 phaseBox
    class P1A,P1B,P1C,P2A,P2B,P2C,P3A,P3B,P3C,P3D,P4A,P4B,P4C,P5A,P5B,P5C,P6A,P6B,P6C actionBox
    class C1,C2,C3,C4 challengeBox
    
    %% 关键里程碑
    Phase3 -.->|关键节点| Milestone1["🎯 领域模型确立<br/>业务逻辑与技术分离"]
    Phase4 -.->|关键节点| Milestone2["🔄 依赖倒置实现<br/>适配器模式应用"]
    Phase6 -.->|关键节点| Milestone3["🏆 架构升级完成<br/>可维护性大幅提升"]
```