package xyz.controller.infrastructure.gateway;

import org.springframework.stereotype.Service;
import xyz.controller.infrastructure.gateway.dto.ProductDTO;

import java.math.BigDecimal;

/**
 * <AUTHOR> https://github.com/World-controller @GoAndRun
 * @description
 * @create 2025/7/25 22:43
 */
@Service
public class ProductRPC {

    public ProductDTO queryProductByProductId(String productId){
        ProductDTO productVO = new ProductDTO();
        productVO.setProductId(productId);
        productVO.setProductName("测试商品");
        productVO.setProductDesc("这是一个测试商品");
        productVO.setPrice(new BigDecimal("1.68"));
        return productVO;
    }

}
