package xyz.controller.infrastructure.gateway;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;
import xyz.controller.infrastructure.gateway.dto.WeixinQrCodeRequestDTO;
import xyz.controller.infrastructure.gateway.dto.WeixinQrCodeResponseDTO;
import xyz.controller.infrastructure.gateway.dto.WeixinTemplateMessageDTO;
import xyz.controller.infrastructure.gateway.dto.WeixinTokenResponseDTO;

/**
 * <AUTHOR> https://github.com/World-controller @GoAndRun
 * @description  微信API服务 retrofit2
 * @create 2025/7/23 17:33
 */
public interface IWeixinApiService {

    /**
     * 获取 Access token（微信访问令牌）
     * 文档：<a href="https://developers.weixin.qq.com/doc/offiaccount/Basic_Information/Get_access_token.html">Get_access_token</a>
     *
     * @param grantType 获取access_token填写client_credential
     * @param appId     第三方用户唯一凭证
     * @param appSecret 第三方用户唯一凭证密钥，即appsecret
     * @return 响应结果
     */
    @GET("cgi-bin/token")//    Retrofit框架的注解，表示这是一个HTTP GET请求
    //完整URL类似：https://api.weixin.qq.com/cgi-bin/token    最终请求URL：/cgi-bin/token?grant_type=client_credential&appid=xxx&secret=yyy
    //Call<WeixinTokenRes>：Retrofit的返回类型，表示异步调用，泛型WeixinTokenRes是响应数据类型
    Call<WeixinTokenResponseDTO> getToken(@Query("grant_type") String grantType,
                                          @Query("appid") String appId,
                                          @Query("secret") String appSecret);

    /**
     * 获取凭据 ticket，创建二维码
     * 文档：<a href="https://developers.weixin.qq.com/doc/offiaccount/Account_Management/Generating_a_Parametric_QR_Code.html">Generating_a_Parametric_QR_Code</a>
     * <a href="https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=TICKET">前端根据凭证展示二维码</a>
     *
     * @param accessToken         getToken 获取的 token 信息
     * @param weixinQrCodeRequestDTO 入参对象
     * @return 应答结果
     */
//    实际http请求
//    POST https://api.weixin.qq.com/cgi-bin/qrcode/create?access_token=ACCESS_TOKEN
//Content-Type: application/json
//
//{
//  "expire_seconds": 2592000,
//  "action_name": "QR_SCENE",
//  "action_info": {
//    "scene": {"scene_id": 100601}
//  }
//}
    @POST("cgi-bin/qrcode/create")//请求格式：POST /cgi-bin/qrcode/create?access_token=xxx，请求体包含JSON数据
//    @Body：将weixinQrCodeReq对象序列化为JSON放入请求体
    Call<WeixinQrCodeResponseDTO> createQrCode(@Query("access_token") String accessToken, @Body WeixinQrCodeRequestDTO weixinQrCodeRequestDTO);

    /**
     * 发送微信公众号模板消息
     * 文档：https://mp.weixin.qq.com/debug/cgi-bin/readtmpl?t=tmplmsg/faq_tmpl
     *
     * @param accessToken              getToken 获取的 token 信息
     * @param weixinTemplateMessageDTO 入参对象
     * @return 应答结果
     */

    //Call<Void>：返回类型为Void，表示不关心响应内容，只关心是否成功
    //@Body：模板消息对象作为请求体
    //实际http请求
    //POST https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=ACCESS_TOKEN
    // Content-Type: application/json
    //
    //{
    //  "touser": "OPENID",
    //  "template_id": "ngqIpbwh8bUfcSsECmogfXcV14J0tQlEpBO27izEYtY",
    //  "url": "https://gaga.plus",
    //  "data": {
    //    "user": {"value": "用户openid"}
    //  }
    //
    @POST("cgi-bin/message/template/send")
    Call<Void> sendMessage(@Query("access_token") String accessToken, @Body WeixinTemplateMessageDTO weixinTemplateMessageDTO);

}
