package xyz.controller.infrastructure.gateway.dto;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> https://github.com/World-controller @GoAndRun
 * @description 微信模板消息 这个类的设计很巧妙，通过嵌套Map和匿名内部类，完美映射了微信模板消息API的数据结构要求。
 * {
 *   "touser": "or0Ab6ivwmypESVp_bYuk92T6SvU",
 *   "template_id": "GLlAM-Q4jdgsktdNd35hnEbHVam2mwsW2YWuxDhpQkU",
 *   "url": "https://weixin.qq.com",
 *   "data": {
 *     "user": {
 *       "value": "某个用户ID"
 *     }
 *   }
 * }
 * @create 2025/7/23 19:21
 */
public class WeixinTemplateMessageDTO {//VO表示Value Object（值对象）
    //接收消息的用户openid
    private String touser = "or0Ab6ivwmypESVp_bYuk92T6SvU";//默认值，这是一个微信用户的openid
    private String template_id = "GLlAM-Q4jdgsktdNd35hnEbHVam2mwsW2YWuxDhpQkU";//默认值是一个具体的模板ID，用于标识使用哪个消息模板
    private String url = "https://github.com/World-controller";//用户点击模板消息后跳转的URL地址    默认跳转到微信官网
    //这个结构用于存储模板消息的动态数据
    private Map<String, Map<String, String>> data = new HashMap<>();

    /**
     * 接收两个参数：用户openid和模板ID
     * @param touser  用户openid
     * @param template_id 模板ID
     */
    public WeixinTemplateMessageDTO(String touser, String template_id) {
        this.touser = touser;
        this.template_id = template_id;
    }

    /**
     *
     * @param key 使用枚举类型作为键
     * @param value 要设置的值
     */
    public void put(TemplateKey key, String value) {
        //key.getCode()：获取枚举的code值作为键
        data.put(key.getCode(), new HashMap<String, String>() {
            private static final long serialVersionUID = 7092338402387318563L;
            {
                put("value", value);//键固定为"value"，值是传入的参数
            }
        });
    }

    /**
     * 这是一个工具方法，可以操作外部传入的Map（逻辑与实例方法相同，但操作的是传入的data参数）
     * @param data  data Map
     * @param key 枚举键
     * @param value 字符串值
     */
    public static void put(Map<String, Map<String, String>> data, TemplateKey key, String value) {
        data.put(key.getCode(), new HashMap<String, String>() {
            private static final long serialVersionUID = 7092338402387318563L;

            {
                put("value", value);
            }
        });
    }


    public enum TemplateKey {
        USER("user","用户ID"),
        ;//分号表示枚举常量定义结束

        private String code;
        private String desc;

        TemplateKey(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }
    }


    public String getTouser() {
        return touser;
    }

    public void setTouser(String touser) {
        this.touser = touser;
    }

    public String getTemplate_id() {
        return template_id;
    }

    public void setTemplate_id(String template_id) {
        this.template_id = template_id;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Map<String, Map<String, String>> getData() {
        return data;
    }

    public void setData(Map<String, Map<String, String>> data) {
        this.data = data;
    }

}
