package xyz.controller.infrastructure.gateway.dto;

import lombok.Data;

/**
 * <AUTHOR> https://github.com/World-controller @GoAndRun
 * @description 获取微信登录二维码响应对象
 * @create 2025/7/23 19:22
 */
@Data
public class WeixinQrCodeResponseDTO {
    //这个ticket是一个字符串，用于生成实际的二维码图片
    // 微信API会返回类似这样的值："gQH47joAAAAAAAAAASxodHRwOi8vd2VpeGluLnFxLmNvbS9xLzAyX..."
    private String ticket;
    // expire_seconds：字段名，表示二维码的过期时间
    private Long expire_seconds;
    // url：字段名，表示二维码对应的URL地址
    //这个URL可以直接用于生成二维码图片
    //微信返回的格式类似："https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=TICKET"
    private String url;

}
