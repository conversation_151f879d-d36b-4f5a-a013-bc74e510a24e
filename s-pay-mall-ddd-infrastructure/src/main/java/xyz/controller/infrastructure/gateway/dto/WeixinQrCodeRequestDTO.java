package xyz.controller.infrastructure.gateway.dto;

import lombok.*;

/**
 * <AUTHOR> https://github.com/World-controller @GoAndRun
 * @description 获取微信登录二维码请求对象  这个类的设计非常精巧，通过嵌套内部类和枚举，完美映射了微信API的JSON结构要求，同时提供了类型安全和代码提示
 * {
 *   "expire_seconds": 2592000,
 *   "action_name": "QR_SCENE",
 *   "action_info": {
 *     "scene": {
 *       "scene_id": 100601,
 *       "scene_str": null
 *     }
 *   }
 * }
 * @create 2025/7/23 19:21
 */
@Data
@Builder//生成建造者模式代码   允许链式调用创建对象：WeixinQrCodeReq.builder().expire_seconds(3600).build()
@AllArgsConstructor
@NoArgsConstructor
public class WeixinQrCodeRequestDTO {
    //表示二维码的过期时间（秒）
    private int expire_seconds;
    //表示二维码类型（如"QR_SCENE"）
    private String action_name;
    // ActionInfo：自定义类型，是下面定义的内部类
    // action_info：字段名，包含二维码的场景信息
    private ActionInfo action_info;

//    第一个内部类：ActionInfo
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ActionInfo {//public static class：声明一个静态内部类
        Scene scene;//字段声明，类型是另一个内部类 Scene 没有显式的访问修饰符，默认为package-private（同包可见）

//    第二个内部类：Scene：嵌套在ActionInfo内的另一个静态内部类
        @Data
        @Builder
        @AllArgsConstructor
        @NoArgsConstructor
        public static class Scene {
            int scene_id;//整型字段，表示场景ID值，用于临时或永久的整型参数二维码
            String scene_str;//字符串字段，表示场景字符串值 用于临时或永久的字符串参数二维码
        }
    }
//枚举类：ActionNameTypeVO
    @Getter
    @AllArgsConstructor
    @NoArgsConstructor
    public enum ActionNameTypeVO {//VO表示"Value Object"（值对象）
        QR_SCENE("QR_SCENE", "临时的整型参数值"),//这个常量表示临时的、使用整型参数的二维码
        QR_STR_SCENE("QR_STR_SCENE", "临时的字符串参数值"),//表示临时的、使用字符串参数的二维码
        QR_LIMIT_SCENE("QR_LIMIT_SCENE", "永久的整型参数值"),//表示永久的、使用整型参数的二维码
        QR_LIMIT_STR_SCENE("QR_LIMIT_STR_SCENE", "永久的字符串参数值");//表示永久的、使用字符串参数的二维码
            //注意最后一个枚举常量用分号结束

        private String code;
        private String info;
    }

}
