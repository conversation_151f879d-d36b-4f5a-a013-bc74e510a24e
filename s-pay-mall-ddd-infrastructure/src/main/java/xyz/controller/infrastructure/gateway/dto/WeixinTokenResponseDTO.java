package xyz.controller.infrastructure.gateway.dto;

import lombok.Data;

/**
 * <AUTHOR> https://github.com/World-controller @GoAndRun
 * @description 获取 Access token DTO 对象
 * @create 2025/7/23 19:22
 */
@Data
public class WeixinTokenResponseDTO {
// access_token：字段名
//这是微信API返回的访问令牌，用于后续调用微信API时的身份验证
    private String access_token;
    //表示访问令牌的有效期，单位是秒  微信通常返回7200，表示2小时（2×60×60=7200秒）过了这个时间，access_token就会失效，需要重新获取
    private int expires_in;
    private String errcode;
    private String errmsg;

}
