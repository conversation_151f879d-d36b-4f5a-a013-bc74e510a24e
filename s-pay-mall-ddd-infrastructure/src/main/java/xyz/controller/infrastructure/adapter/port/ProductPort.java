package xyz.controller.infrastructure.adapter.port;

import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;
import xyz.controller.domain.order.adapter.port.IProductPort;
import xyz.controller.domain.order.model.entity.ProductEntity;
import xyz.controller.infrastructure.gateway.ProductRPC;
import xyz.controller.infrastructure.gateway.dto.ProductDTO;

/**
 * <AUTHOR> https://github.com/World-controller @GoAndRun
 * @description
 * @create 2025/8/3 20:12
 */
@Component
public class ProductPort implements IProductPort {

    private final ProductRPC productRPC;



    public ProductPort(ProductRPC productRPC) {
        this.productRPC = productRPC;
    }

    @Override
    public ProductEntity queryProductByProductId(String productId) {

        ProductDTO productDTO = productRPC.queryProductByProductId(productId);

        return ProductEntity.builder()
                .productId(productId)
                .productName(productDTO.getProductName())
                .productDesc(productDTO.getProductDesc())
                .price(productDTO.getPrice())
                .build();
    }
}
