package xyz.controller.infrastructure.adapter.port;

import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayClient;
import com.alipay.api.request.AlipayTradePagePayRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import xyz.controller.domain.order.adapter.port.IPaymentPort;
import xyz.controller.domain.order.adapter.port.IProductPort;
import xyz.controller.domain.order.adapter.repository.IOrderRepository;
import xyz.controller.domain.order.model.entity.PayOrderEntity;
import xyz.controller.domain.order.model.entity.PaymentEntity;
import xyz.controller.domain.order.model.valobj.OrderStatusVO;

import javax.annotation.Resource;

/**
 * <AUTHOR> https://github.com/World-controller @GoAndRun
 * @description
 * @create 2025/8/5 13:37
 */
@Slf4j
@Service
public class AlipayPaymentPort implements IPaymentPort {

    protected final IOrderRepository repository;//往我们库里写

    public AlipayPaymentPort(IOrderRepository repository) {
        this.repository = repository;
    }

    @Resource
    private AlipayClient alipayClient;

    @Override
    public PayOrderEntity createPayOrder(PaymentEntity paymentEntity) throws Exception {
        log.info("支付宝支付适配器 - 创建支付订单: {}", paymentEntity.getOrderId());

        AlipayTradePagePayRequest request = new AlipayTradePagePayRequest();
        request.setNotifyUrl(paymentEntity.getNotifyUrl());
        request.setReturnUrl(paymentEntity.getReturnUrl());

        JSONObject bizContent = new JSONObject();
        bizContent.put("out_trade_no", paymentEntity.getOrderId());
        bizContent.put("total_amount", paymentEntity.getTotalAmount().toString());
        bizContent.put("subject", paymentEntity.getProductName());
        bizContent.put("product_code", "FAST_INSTANT_TRADE_PAY");
        request.setBizContent(bizContent.toString());

        // 调用支付宝SDK，创建支付订单
        String form = alipayClient.pageExecute(request).getBody();

        PayOrderEntity payOrderEntity = PayOrderEntity.builder()
                .orderId(paymentEntity.getOrderId())
                .payUrl(form)
                .orderStatus(OrderStatusVO.PAY_WAIT)
                .build();
        //更新数据库，如果调用sdk成功，但更新数据库失败，那么我们会用补偿任务保证事务的一致性
        repository.updateOrderPayInfo(payOrderEntity);
        return payOrderEntity;
    }
}
