package xyz.controller.infrastructure.adapter.port;

import com.google.common.cache.Cache;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import retrofit2.Call;
import xyz.controller.domain.auth.adapter.port.ILoginPort;
import xyz.controller.infrastructure.gateway.IWeixinApiService;
import xyz.controller.infrastructure.gateway.dto.WeixinQrCodeRequestDTO;
import xyz.controller.infrastructure.gateway.dto.WeixinQrCodeResponseDTO;
import xyz.controller.infrastructure.gateway.dto.WeixinTemplateMessageDTO;
import xyz.controller.infrastructure.gateway.dto.WeixinTokenResponseDTO;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> https://github.com/World-controller @GoAndRun
 * @description
 * @create 2025/8/3 8:28
 */
@Service
public class LoginPort implements ILoginPort {

    @Value("${weixin.config.app-id}")
    private String appid;
    @Value("${weixin.config.app-secret}")
    private String appSecret;
    @Value("${weixin.config.template_id}")
    private String template_id;
    //    Guava缓存对象，用来存储微信的访问令牌
    @Resource
    private Cache<String, String> weixinAccessToken;
    // Spring 注入 Retrofit 生成的实现
    @Resource
    private IWeixinApiService weixinApiService;

    @Override
    public String createQrCodeTicket() throws IOException {
        // 1. 获取 accessToken：根据应用ID从缓存中获取访问令牌，如果不存在返回null
        String accessToken = weixinAccessToken.getIfPresent(appid);//从guava缓存中根据appid获取到accessToken
        if (null == accessToken) {//检查缓存中是否有有效的访问令牌
//            微信API服务接口，用Retrofit框架实现HTTP调用   "client_credential"：固定参数，表示获取访问令牌的授权类型
//            1.调用获取 token 的方法
            Call<WeixinTokenResponseDTO> call = weixinApiService.getToken("client_credential", appid, appSecret);
            // 2. 执行同步调用
            WeixinTokenResponseDTO weixinTokenRes = call
                    .execute()//call.execute()：同步执行HTTP请求
                    .body();//body()：获取响应体内容
            assert weixinTokenRes != null;//确保微信API返回了有效响应
            accessToken = weixinTokenRes.getAccess_token();//从响应对象中提取访问令牌字符串
            weixinAccessToken.put(appid, accessToken);//将获取到的访问令牌存入缓存，以appid为键
        }

        // 2. 生成 ticket(调用创建二维码的方法)
        WeixinQrCodeRequestDTO weixinQrCodeReq = WeixinQrCodeRequestDTO.builder()//使用建造者模式创建微信二维码请求对象
                .expire_seconds(2592000)//设置二维码过期时间为2592000秒（30天）
                .action_name(WeixinQrCodeRequestDTO.ActionNameTypeVO.QR_SCENE.getCode())//设置二维码类型为临时场景二维码   QR_SCENE 枚举值，表示临时场景二维码类型
                .action_info(WeixinQrCodeRequestDTO.ActionInfo.builder()
                        .scene(WeixinQrCodeRequestDTO.ActionInfo.Scene.builder()//设置二维码的场景信息
                                .scene_id(100601)//scene_id(100601)：场景ID，用于标识不同的二维码用途
                                .build())
                        .build())
                .build();

        Call<WeixinQrCodeResponseDTO> call = weixinApiService.createQrCode(accessToken, weixinQrCodeReq);//调用微信API创建二维码
        WeixinQrCodeResponseDTO weixinQrCodeRes = call.execute().body();//同步执行请求并获取响应体
        assert null != weixinQrCodeRes;//断言确保获得了有效的二维码响应
        return weixinQrCodeRes.getTicket();//data: 微信二维码票据字符串，用于生成实际的二维码图片
    }

    @Override
    public void sendLoginTemplate(String openid) throws IOException {
// 1. 获取 accessToken 【实际业务场景，按需处理下异常】
        String accessToken = weixinAccessToken.getIfPresent(appid);
        if (null == accessToken){
            //重新获取accessToken的逻辑，与第一个方法相同
            Call<WeixinTokenResponseDTO> call = weixinApiService.getToken("client_credential", appid, appSecret);
            WeixinTokenResponseDTO weixinTokenRes = call.execute().body();
            assert weixinTokenRes != null;
            //再次获取访问令牌，因为发送模板消息也需要令牌
            accessToken = weixinTokenRes.getAccess_token();
            weixinAccessToken.put(appid, accessToken);
        }

        // 2. 发送模板消息
//        2.1创建模板消息的数据结构，微信模板消息需要特定的数据格式
        Map<String, Map<String, String>> data = new HashMap<>();
//        2.2 向数据中添加用户信息，TemplateKey.USER是模板中的占位符
        WeixinTemplateMessageDTO.put(data, WeixinTemplateMessageDTO.TemplateKey.USER, openid);
//        2.3 创建模板消息对象，指定接收用户和模板ID
        WeixinTemplateMessageDTO templateMessageDTO = new WeixinTemplateMessageDTO(openid, template_id);
//        2.4 设置模板消息点击后跳转的URL
        templateMessageDTO.setUrl("https://gaga.plus");
//        2.5 设置模板消息的数据内容
        templateMessageDTO.setData(data);
//        3.调用微信API发送模板消息给用户，通知登录成功
        Call<Void> call = weixinApiService.sendMessage(accessToken, templateMessageDTO);
        call.execute();
    }
}
