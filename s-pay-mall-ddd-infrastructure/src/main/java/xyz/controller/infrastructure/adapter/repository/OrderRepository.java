package xyz.controller.infrastructure.adapter.repository;

import com.alibaba.fastjson.JSON;
import com.google.common.eventbus.EventBus;
import org.springframework.stereotype.Repository;
import xyz.controller.domain.order.adapter.repository.IOrderRepository;
import xyz.controller.domain.order.model.aggregate.CreateOrderAggregate;
import xyz.controller.domain.order.model.entity.OrderEntity;
import xyz.controller.domain.order.model.entity.PayOrderEntity;
import xyz.controller.domain.order.model.entity.ProductEntity;
import xyz.controller.domain.order.model.entity.ShopCartEntity;
import xyz.controller.domain.order.model.valobj.OrderStatusVO;
import xyz.controller.infrastructure.adapter.event.PaySuccessMessageEvent;
import xyz.controller.infrastructure.dao.IOrderDao;
import xyz.controller.infrastructure.dao.po.PayOrder;
import xyz.controller.types.event.BaseEvent;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> https://github.com/World-controller @GoAndRun
 * @description
 * @create 2025/8/3 20:17
 */
@Repository
public class OrderRepository implements IOrderRepository {

    @Resource
    private IOrderDao orderDao;

    @Resource
    private PaySuccessMessageEvent paySuccessMessageEvent;

    @Resource
    private EventBus eventBus;

    @Override
    public void doSaveOrder(CreateOrderAggregate orderAggregate) {
        String userId = orderAggregate.getUserId();
        ProductEntity productEntity = orderAggregate.getProductEntity();
        OrderEntity orderEntity = orderAggregate.getOrderEntity();

        PayOrder order = PayOrder.builder()
                .userId(userId)
                .productId(productEntity.getProductId())
                .productName(productEntity.getProductName())
                .orderId(orderEntity.getOrderId())
                .orderTime(orderEntity.getOrderTime())
                .totalAmount(productEntity.getPrice())
                .status(orderEntity.getOrderStatusVO().getCode())
                .build();
        orderDao.insert(order);
    }

    @Override
    public OrderEntity queryUnPayOrder(ShopCartEntity shopCartEntity) {
//        1.封装参数
        PayOrder orderReq = new PayOrder();
        orderReq.setUserId(shopCartEntity.getUserId());
        orderReq.setProductId(shopCartEntity.getProductId());
//        2.查询到订单
        PayOrder order = orderDao.queryUnPayOrder(orderReq);
        if (order == null) return null;
//        3.返回结果
        return OrderEntity.builder()
                .productId(order.getProductId())
                .productName(order.getProductName())
                .orderId(order.getOrderId())
                .orderStatusVO(OrderStatusVO.valueOf(order.getStatus()))
                .orderTime(order.getOrderTime())
                .totalAmount(order.getTotalAmount())
                .payUrl(order.getPayUrl())
                .build();


    }

    @Override
    public void updateOrderPayInfo(PayOrderEntity payOrderEntity) {
        PayOrder payOrder = PayOrder.builder()
                .userId(payOrderEntity.getUserId())
                .orderId(payOrderEntity.getOrderId())
                .status(payOrderEntity.getOrderStatus().getCode())
                .payUrl(payOrderEntity.getPayUrl())
                .build();
        orderDao.updateOrderPayInfo(payOrder);
    }

    @Override
    public void changeOrderPaySuccess(String orderId) {
        // 1. 构建更新对象
        PayOrder payOrder = new PayOrder();
        payOrder.setOrderId(orderId);
        payOrder.setStatus(OrderStatusVO.PAY_SUCCESS.getCode());
        // 2. 更新数据库
        orderDao.changeOrderPaySuccess(payOrder);
        // 3. 发布支付成功事件  事件内容：订单信息的JSON字符串 {
        //  "orderId": "1234567890123456",
        //  "status": "PAY_SUCCESS"
        //}   监听器：OrderPaySuccessListener 会处理后续业务（发货、积分等）
        BaseEvent.EventMessage<PaySuccessMessageEvent.PaySuccessMessage> paySuccessMessageEventMessage = paySuccessMessageEvent.buildEventMessage(PaySuccessMessageEvent.PaySuccessMessage.builder().tradeNo(orderId).build());
        PaySuccessMessageEvent.PaySuccessMessage paySuccessMessage = paySuccessMessageEventMessage.getData();
        eventBus.post(paySuccessMessage);

    }

    @Override
    public List<String> queryNoPayNotifyOrder() {
        return orderDao.queryNoPayNotifyOrder();
    }

    @Override
    public List<String> queryTimeoutCloseOrderList() {
        return orderDao.queryTimeoutCloseOrderList();
    }

    @Override
    public boolean changeOrderClose(String orderId) {
        return orderDao.changeOrderClose(orderId);
    }

}
