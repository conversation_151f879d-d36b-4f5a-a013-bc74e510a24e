package xyz.controller.application.service;

import xyz.controller.domain.order.model.entity.PayOrderEntity;
import xyz.controller.domain.order.model.entity.ShopCartEntity;

import java.util.Map;

/**
 * <AUTHOR> href="https://github.com/AdlerED">Adler</a>
 * @description 订单应用服务接口
 * @create 2024-05-22 10:31
 */
public interface IOrderApplicationService {

    /**
     * 关闭超时订单 (业务用例)
     * @param orderId 订单ID
     */
    void closeTimeoutOrder(String orderId);

    /**
     * 创建订单
     * @param shopCartEntity 购物车实体
     * @return 支付订单实体
     */
    PayOrderEntity createOrder(ShopCartEntity shopCartEntity) throws Exception;

    /**
     * 处理支付回调通知
     * @param params 回调参数
     * @return 处理结果
     */
    String handlePaymentNotify(Map<String, String> params);

    /**
     * 检查并更新支付状态
     */
    void checkAndUpdatePaymentStatus();

}