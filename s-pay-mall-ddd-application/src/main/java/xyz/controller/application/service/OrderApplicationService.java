package xyz.controller.application.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xyz.controller.domain.order.adapter.port.IPaymentPort;
import xyz.controller.domain.order.model.entity.PayOrderEntity;
import xyz.controller.domain.order.model.entity.ShopCartEntity;
import xyz.controller.domain.order.service.IOrderService;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> href="https://github.com/AdlerED">Adler</a>
 * @description 订单应用服务
 * @create 2024-05-22 10:31
 */
@Slf4j
@Service
public class OrderApplicationService implements IOrderApplicationService {

    @Resource
    private IOrderService orderService;

    @Resource
    private IPaymentPort paymentPort;

    /**
     * 关闭超时订单 (业务用例)
     * @param orderId 订单ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void closeTimeoutOrder(String orderId) {
        orderService.changeOrderClose(orderId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PayOrderEntity createOrder(ShopCartEntity shopCartEntity) throws Exception {
        return orderService.createOrder(shopCartEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String handlePaymentNotify(Map<String, String> params) {
        try {
            // 验证支付回调
            IPaymentPort.PaymentCallbackResult result = paymentPort.verifyPaymentCallback(params);

            if (!result.isValid()) {
                log.warn("支付回调验证失败: {}", result.getMessage());
                return "false";
            }

            // 更新订单状态
            orderService.changeOrderPaySuccess(result.getOrderId());
            log.info("支付回调处理成功，订单号: {}", result.getOrderId());
            return "success";

        } catch (Exception e) {
            log.error("处理支付回调失败", e);
            return "false";
        }
    }

    @Override
    public void checkAndUpdatePaymentStatus() {
        try {
            // 查询可能丢失通知的订单
            List<String> orderIds = orderService.queryNoPayNotifyOrder();
            if (orderIds == null || orderIds.isEmpty()) {
                return;
            }

            // 主动查询支付宝支付状态
            for (String orderId : orderIds) {
                boolean isPaid = paymentPort.queryPaymentStatus(orderId);
                if (isPaid) {
                    orderService.changeOrderPaySuccess(orderId);
                    log.info("补偿更新订单支付状态成功，订单号: {}", orderId);
                }
            }
        } catch (Exception e) {
            log.error("检查并更新支付状态失败", e);
        }
    }
}