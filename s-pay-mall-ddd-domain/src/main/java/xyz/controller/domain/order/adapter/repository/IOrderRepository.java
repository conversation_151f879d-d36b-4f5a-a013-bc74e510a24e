package xyz.controller.domain.order.adapter.repository;

import xyz.controller.domain.order.model.aggregate.CreateOrderAggregate;
import xyz.controller.domain.order.model.entity.OrderEntity;
import xyz.controller.domain.order.model.entity.PayOrderEntity;
import xyz.controller.domain.order.model.entity.ShopCartEntity;

import java.util.List;

/**
 * <AUTHOR> https://github.com/World-controller @GoAndRun
 * @description 所谓适配器就是一个管子，右边小需要通过适配器才能够在抵达左边之前变大
 * @create 2025/8/3 18:19
 */
public interface IOrderRepository {
    void doSaveOrder(CreateOrderAggregate orderAggregate);

    OrderEntity queryUnPayOrder(ShopCartEntity shopCartEntity);

    void updateOrderPayInfo(PayOrderEntity payOrderEntity);

    void changeOrderPaySuccess(String orderId);

    List<String> queryNoPayNotifyOrder();

    List<String> queryTimeoutCloseOrderList();

    boolean changeOrderClose(String orderId);
}
