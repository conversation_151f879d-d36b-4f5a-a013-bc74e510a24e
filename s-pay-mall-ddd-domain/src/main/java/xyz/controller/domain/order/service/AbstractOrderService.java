package xyz.controller.domain.order.service;

import com.alipay.api.AlipayApiException;
import lombok.extern.slf4j.Slf4j;
import xyz.controller.domain.order.adapter.port.IProductPort;
import xyz.controller.domain.order.adapter.repository.IOrderRepository;
import xyz.controller.domain.order.model.aggregate.CreateOrderAggregate;
import xyz.controller.domain.order.model.entity.OrderEntity;
import xyz.controller.domain.order.model.entity.PayOrderEntity;
import xyz.controller.domain.order.model.entity.ProductEntity;
import xyz.controller.domain.order.model.entity.ShopCartEntity;
import xyz.controller.domain.order.model.valobj.OrderStatusVO;

import java.math.BigDecimal;

/**
 * <AUTHOR> https://github.com/World-controller @GoAndRun
 * @description 一、单一职责原则
 * 1.AbstractOrderService：负责订单创建的业务流程编排
 * 2.具体实现类：负责订单保存的具体策略
 * 3.Repository：负责数据持久化
 * 二、抽象类的作用：
 * 1.定义标准流程：订单创建的业务流程是固定的（检查重复订单 → 查询商品 → 创建订单 → 保存订单）
 * 2.保证一致性：所有订单服务都必须遵循这个标准流程
 * 3.扩展点预留：doSaveOrder() 作为扩展点，允许不同的实现方式
 * <p>
 * //    三、可能的未来扩展
 * // 1.VIP订单的特殊保存逻辑：优先级处理、特殊折扣等
 * // 2.团购订单的特殊保存逻辑：库存锁定、团购状态等
 * @create 2025/8/3 18:16
 */
@Slf4j
public abstract class AbstractOrderService implements IOrderService {

    protected final IOrderRepository repository;//往我们库里写

    protected final IProductPort port;//从外部拉取数据

    public AbstractOrderService(IOrderRepository repository, IProductPort port) {
        this.repository = repository;
        this.port = port;
    }

    @Override
    public PayOrderEntity createOrder(ShopCartEntity shopCartEntity) throws Exception {
//        1.查询当前用户是否存在调单和未支付订单
        OrderEntity unpaidOrderEntity = repository.queryUnPayOrder(shopCartEntity);
        if (unpaidOrderEntity != null && OrderStatusVO.PAY_WAIT.equals(unpaidOrderEntity.getOrderStatusVO())) {
            log.info("创建订单-存在已存在未支付订单。userId：{} productId:{} orderId:{}", shopCartEntity.getUserId(), shopCartEntity.getProductId(), unpaidOrderEntity.getOrderId());
            return PayOrderEntity.builder()
                    .orderId(unpaidOrderEntity.getOrderId())
                    .payUrl(unpaidOrderEntity.getPayUrl())
                    .build();
            //出现调单（用户订单信息已经存入数据库，但是由于网络原因未加载出url）CREATE
        } else if (null != unpaidOrderEntity && OrderStatusVO.CREATE.equals(unpaidOrderEntity.getOrderStatusVO())) {
            //创建支付单
            log.info("创建订单-存在已存在未支付订单。userId：{} productId:{} orderId:{}", shopCartEntity.getUserId(), shopCartEntity.getProductId(), unpaidOrderEntity.getOrderId());
            PayOrderEntity payOrderEntity = doPrepayOrder(shopCartEntity.getUserId(), shopCartEntity.getProductId(), unpaidOrderEntity.getProductName(), unpaidOrderEntity.getOrderId(), unpaidOrderEntity.getTotalAmount());
            return PayOrderEntity.builder()
                    .orderId(payOrderEntity.getOrderId())
                    .payUrl(payOrderEntity.getPayUrl())
                    .build();
        }
        //        2.查询商品&&创建订单
        ProductEntity productEntity = port.queryProductByProductId(shopCartEntity.getProductId());

        OrderEntity orderEntity = CreateOrderAggregate.buildOrderEntity(productEntity.getProductId(), productEntity.getProductName());

        CreateOrderAggregate orderAggregate = CreateOrderAggregate.builder()
                .userId(shopCartEntity.getUserId())
                .productEntity(productEntity)
                .orderEntity(orderEntity)
                .build();
        // 3.调用抽象方法，由子类实现具体保存逻辑
        this.doSaveOrder(orderAggregate);

        PayOrderEntity payOrderEntity = doPrepayOrder(shopCartEntity.getUserId(), shopCartEntity.getProductId(), orderEntity.getProductName(), orderEntity.getOrderId(), productEntity.getPrice());
        return PayOrderEntity.builder()
                .orderId(payOrderEntity.getOrderId())
                .payUrl(payOrderEntity.getPayUrl())
                .build();
    }

    protected abstract void doSaveOrder(CreateOrderAggregate orderAggregate);

    protected abstract PayOrderEntity doPrepayOrder(String userId, String productId, String productName, String orderId, BigDecimal totalAmount) throws Exception;

}
