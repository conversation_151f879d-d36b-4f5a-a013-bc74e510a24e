package xyz.controller.domain.order.adapter.port;

import xyz.controller.domain.order.model.entity.PayOrderEntity;
import xyz.controller.domain.order.model.entity.PaymentEntity;

/**
 * <AUTHOR> https://github.com/World-controller @GoAndRun
 * @description 支付端口接口 - Domain层定义的抽象
 * @create 2025/8/5 13:31
 */
public interface IPaymentPort {

    /**
     * 创建支付订单
     * @param paymentEntity 支付请求
     * @return 支付订单实体
     */
    PayOrderEntity createPayOrder(PaymentEntity paymentEntity) throws Exception;


}
