package xyz.controller.domain.order.service;

import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.request.AlipayTradePagePayRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import xyz.controller.domain.order.adapter.port.IPaymentPort;
import xyz.controller.domain.order.adapter.port.IProductPort;
import xyz.controller.domain.order.adapter.repository.IOrderRepository;
import xyz.controller.domain.order.model.aggregate.CreateOrderAggregate;
import xyz.controller.domain.order.model.entity.PayOrderEntity;
import xyz.controller.domain.order.model.entity.PaymentEntity;
import xyz.controller.domain.order.model.valobj.OrderStatusVO;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> https://github.com/World-controller @GoAndRun
 * @description
 * @create 2025/8/3 19:26
 */
@Slf4j
@Service
public class OrderService extends AbstractOrderService {

    @Value("${alipay.notify_url}")
    private String notifyUrl;
    @Value("${alipay.return_url}")
    private String returnUrl;

    @Resource
    private IPaymentPort paymentPort;

    public OrderService(IOrderRepository repository, IProductPort port) {
        super(repository, port);
    }

    @Override
    protected void doSaveOrder(CreateOrderAggregate orderAggregate) {
        repository.doSaveOrder(orderAggregate);
    }

    @Override
    protected PayOrderEntity doPrepayOrder(String userId, String productId, String productName, String orderId, BigDecimal totalAmount) throws Exception {
        // ✅ 使用Domain层的值对象
        PaymentEntity paymentEntity = PaymentEntity.builder()
                .userId(userId)
                .productId(productId)
                .productName(productName)
                .orderId(orderId)
                .totalAmount(totalAmount)
                .notifyUrl(notifyUrl)
                .returnUrl(returnUrl)
                .build();
// ✅ 通过端口接口调用，而不是直接调用AlipayClient
        return paymentPort.createPayOrder(paymentEntity);
    }

    @Override
    public void changeOrderPaySuccess(String orderId) {
        repository.changeOrderPaySuccess(orderId);
    }

    @Override
    public List<String> queryNoPayNotifyOrder() {
        return repository.queryNoPayNotifyOrder();
    }

    @Override
    public List<String> queryTimeoutCloseOrderList() {
        return repository.queryTimeoutCloseOrderList();
    }

    @Override
    public boolean changeOrderClose(String orderId) {
        return repository.changeOrderClose(orderId);
    }
}
