package xyz.controller.domain.order.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR> https://github.com/World-controller @GoAndRun
 * @description 支付请求对象
 * @create 2025/8/5 13:33
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PaymentEntity {
    private String userId;
    private String productId;
    private String productName;
    private String orderId;
    private BigDecimal totalAmount;
    private String notifyUrl;
    private String returnUrl;
}
