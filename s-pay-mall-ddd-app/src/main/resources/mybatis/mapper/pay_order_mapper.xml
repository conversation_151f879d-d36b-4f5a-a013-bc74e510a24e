<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xyz.controller.infrastructure.dao.IOrderDao">

    <resultMap id="dataMap" type="xyz.controller.infrastructure.dao.po.PayOrder">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="product_id" property="productId"/>
        <result column="product_name" property="productName"/>
        <result column="order_id" property="orderId"/>
        <result column="order_time" property="orderTime"/>
        <result column="total_amount" property="totalAmount"/>
        <result column="status" property="status"/>
        <result column="pay_url" property="payUrl"/>
        <result column="pay_time" property="payTime"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <insert id="insert" parameterType="xyz.controller.infrastructure.dao.po.PayOrder">
        insert into pay_order(user_id, product_id, product_name, order_id, order_time,
        total_amount, status, create_time, update_time)
        values(#{userId}, #{productId}, #{productName}, #{orderId}, #{orderTime},
        #{totalAmount}, #{status}, now(), now())
    </insert>
    <update id="updateOrderPayInfo" parameterType="xyz.controller.infrastructure.dao.po.PayOrder">
        update pay_order set pay_url = #{payUrl}, status = #{status}, update_time = now()
        where order_id = #{orderId}
    </update>

    <select id="queryUnPayOrder" parameterType="xyz.controller.infrastructure.dao.po.PayOrder" resultMap="dataMap">
        select product_id, product_name, order_id, order_time, total_amount, status, pay_url
        from pay_order
        where user_id = #{userId} and product_id = #{productId}
        order by id desc
        limit 1
    </select>

    <update id="changeOrderPaySuccess" parameterType="xyz.controller.infrastructure.dao.po.PayOrder">
        update pay_order set status = #{status}, pay_time = now(), update_time = now()
        where order_id = #{orderId}
    </update>
    <!--    查询逻辑：查找状态为PAY_WAIT且创建时间超过1分钟的订单，这些订单可能存在回调丢失的情况。-->
    <select id="queryNoPayNotifyOrder" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT order_id as orderId FROM pay_order
        WHERE status = 'PAY_WAIT' AND NOW() >= order_time + interval 1 MINUTE
        ORDER BY id ASC
        LIMIT 10<!--每次最多处理10个订单，避免一次性处理过多-->
    </select>

    <select id="queryTimeoutCloseOrderList" parameterType="java.lang.String" resultType="java.lang.String" >
        SELECT order_id as orderId FROM pay_order
        WHERE status = 'PAY_WAIT' AND NOW() >= order_time + INTERVAL 30 MINUTE <!--订单创建时间超过30分钟-->
        ORDER BY id ASC<!--按ID升序，优先处理早期订单-->
        LIMIT 50<!--每次最多处理50个订单，避免一次性处理过多-->
    </select>

    <update id="changeOrderClose" parameterType="java.lang.String">
        update pay_order set status = 'CLOSE', pay_time = now(), update_time = now()
        where order_id = #{orderId}
    </update>

</mapper>
