server:
  port: 8080

# 线程池配置
thread:
  pool:
    executor:
      config:
        core-pool-size: 20
        max-pool-size: 50
        keep-alive-time: 5000
        block-queue-size: 5000
        policy: CallerRunsPolicy

# 微信公众号对接
weixin:
  config:
    originalid: gh_974dc599d727
    token: 1234
    app-id: wx20880971f7af9aa4
    app-secret: 2b5977718efa4eb30c248570ec8d29db
    template_id: oir3M4Z8VSZpp7qh7yXN2oMGJlm6cNHmyaj0xn2-bpk

# 支付宝支付 - 沙箱 https://opendocs.alipay.com/common/02kkv7
alipay:
  enabled: true
  app_id: 9021000150649259
  merchant_private_key: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCJi9Ke5MUYN5kbY+jgwANs5/m29WtNJ0OgvMVQFL9AkuwBGcmNJTa+jK6LmLBXrohDFuMs9h4c037Tfc0FbV5i7wLw7Y1xf1Ycs8upCe5G/G/1VT4qZLatop40p2EsCneyZeK+UgXj5mdgWGHpXGMmqg6FXhoqsJurAOvtv9tAGqwvdKUcibn7MwglrbWvBhmzPEouqA2lISmdcyODwf25WRJ2GOczaHtZ95bWD+dKB4wZRBfM3p7wZXiXMJKgHlg9XuYP3a6vUwxm8uu2ArIrCa3gV4CcTbhv9Shu/WzwvHTMmTrTysqesu+hd8vHwCl9YKxRW3OSeNmqNwzfW53dAgMBAAECggEAP3/9zL1xAK0qUird6GS9MBoofv7uesAVyK9dqIrrgLRToG53TmJP08i/DFOBlflNjyuR1cAKRuSdOKZrcIX9fU863F/PXQacisO20mNzTfJc/bZM7OVyMNdSkpxzkBGFESdYduUHMx+7ug8TpNiyLbd03L2E9xBxzZCvWyOn5P++GIlmRD9PcHN3TG5FXjRDJF9pUwH9StptRG4RUBnSNGI0UQ6RemehGIKWBXEKhQ4dD+oSKTVdKSt1rjel1V2RuVTJOZcbzTJRS8miYvXHqnpdok4n00J5jPQwyPOAj30wSio1ZvI4Iqpu84D8nFo9bQclSYLGK8yXDr5pyPRgAQKBgQC9q/SjmraM8W9mrS5mpFrn345WD6S3tlO9/p8F7LlbsPGwK91vIiCZgXnU2SXrcAoofYl9m/qobNhOCZkPAlhDfVGuQCSzFTHKqWQmcEkPG9NoerbLbz1IQYBiZXWC+evJzhIYk2eZ9MQZeL40LYE/i+r2sqytG0yJRWLyxMTEXQKBgQC5pWqgU1fQtljAeSCHY4fZfL7CjWLGUsgSanoKYbxlQ2QyVqmlNTbc1eNNw/7VFPENQPcZSISaVXMu8E4a2+IuAez59RWJK9AK7ajp/Rfn1E4qFCpv2bgy3CpVspT3EguPLfTt2bTUs2M/gC4EMsJIgtE0mVWy+69V+908SsyngQKBgQCvsi33r0US4kMzdZDZ+71JtUiAaBVJKFgCU26zakl5qjr32mpQm+heyhU1GcGKikqen0tRU7+pbMoh37ossRtZySh0pW5ZhXSOKsNe5/yFRl1bFvFlGTJHUIXAoANuxFuqoH0aztDVSGquva5TXyOu3clVpMVXxUXskp3KDIPI8QKBgAaXeAS/UNPpVZdn9Z65Cv7O7hsMWCCAmfgFbdXmiIDz9p/O8fEaxZrrwUlmlyrLto9o6h0stQCh3hghkjRj8FD/4oExkZUwSA/LRn8gbHPf1vKpiqQ0D9VGef5W+/EV4/EuiY7UdxZCVFjR3VBq2PkoI/OBKkjTpPMhoUZdxYyBAoGAMTjOLua80SWALy0BiRgvVhcVMnmN5Q/a/n11+r52nnuwKTzHmvzUjCKgNaqXJe6YfGhmCuUOhqp2/g6EhpN7f+XgHYKj3s/rXcVnGLmJuzyGbBW+WBCGnzE8JOdwVJQKi9aWLEXHXKH19qGQVWmJb43W7QUyQg9qSL712swQW7o=
  alipay_public_key: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAnTjVAwqdPQvnS0lnzkDZ1+P10RrNIQJZe1KzXQKd2tnmNeuACvIOGLqa2qINsi2KPrWrnlVF3XRkeGBiZ5iiD5dU5quhEAqTovUbOsTdMZb+ngBJa7QGGTj3+z7XwBSfAmsj3HTrugZvFtSVvpR8cuP+n/Eiydhw07dqqRxbjESxvaxL8KpILsZyXHvyZr1gtuo/XaiOZZ+K6yTOpln4iCgazPxFEkn1YQZErDF+WKYFecSA4Uhwl+r1pnn/A20Cb4PiTQVbhmtIO80OEc2t/dfEtFcp7scrdj0Y+OcF35jT1tZMlQhU9mxNNzPv41tgyA4ZLLzY+LufIO1ldtVrXQIDAQAB
  notify_url: http://o0-goandrun.natapp1.cc/api/v1/alipay/alipay_notify_url
  return_url: https://github.com/World-controller
  gatewayUrl: https://openapi-sandbox.dl.alipaydev.com/gateway.do
  sign_type: RSA2
  charset: utf-8

# 数据库配置；启动时配置数据库资源信息
spring:
  datasource:
    username: root
    password: 1234
    url: ******************************************************************************************************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
  hikari:
    pool-name: Retail_HikariCP
    minimum-idle: 15 #最小空闲连接数量
    idle-timeout: 180000 #空闲连接存活最大时间，默认600000（10分钟）
    maximum-pool-size: 25 #连接池最大连接数，默认是10
    auto-commit: true  #此属性控制从池返回的连接的默认自动提交行为,默认值：true
    max-lifetime: 1800000 #此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认1800000即30分钟
    connection-timeout: 30000 #数据库连接超时时间,默认30秒，即30000
    connection-test-query: SELECT 1
  type: com.zaxxer.hikari.HikariDataSource

# MyBatis 配置【如需使用记得打开】
mybatis:
  mapper-locations: classpath:/mybatis/mapper/*.xml
  config-location:  classpath:/mybatis/config/mybatis-config.xml

# 日志
logging:
  level:
    root: info
  config: classpath:logback-spring.xml