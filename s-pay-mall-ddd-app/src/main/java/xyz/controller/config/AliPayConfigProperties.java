package xyz.controller.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR> https://github.com/World-controller @GoAndRun
 * @description
 * @create 2025/7/26 9:36
 */
@Data
@ConfigurationProperties(prefix = "alipay", ignoreInvalidFields = true)
public class AliPayConfigProperties {
    // 「沙箱环境」应用ID - 您的APPID，收款账号既是你的APPID对应支付宝账号。获取地址；https://open.alipay.com/develop/sandbox/app
    public String app_id;
    // 「沙箱环境」商户私钥，你的PKCS8格式RSA2私钥
    public String merchant_private_key;
    // 「沙箱环境」支付宝公钥
    public String alipay_public_key;
    // 「沙箱环境」服务器异步通知页面路径
    public String notify_url;
    // 「沙箱环境」页面跳转同步通知页面路径 需http://格式的完整路径，不能加?id=123这类自定义参数，必须外网可以正常访问
    public String return_url;
    // 「沙箱环境」
    public String gatewayUrl;
    // 签名方式
    public String sign_type;
    // 字符编码格式
    public String charset;
    // 传输格式
    private String format = "json";

}
