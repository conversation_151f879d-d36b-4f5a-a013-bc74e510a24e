package xyz.controller.config;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.eventbus.EventBus;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import xyz.controller.trigger.listener.OrderPaySuccessListener;

import java.util.concurrent.TimeUnit;

@Configuration
public class GuavaConfig {

    //    缓存微信 access_token，避免频繁调用微信API，access_token通常有效期为2小时，所以需要缓存来减少请求次数
    @Bean(name = "weixinAccessToken")
    //Cache<String, String>：返回类型，是Guava提供的缓存接口
    //第一个String：缓存的键(Key)类型，这里是字符串（可能是应用ID appid）
    //第二个String：缓存的值(Value)类型，这里也是字符串（访问令牌 access_token）
    public Cache<String, String> weixinAccessToken() {
        return CacheBuilder.newBuilder()
                .expireAfterWrite(2, TimeUnit.HOURS)//这行代码设置缓存中的条目在写入2小时后自动失效
                .build();//完成缓存的构建并返回Cache对象
    }
    //缓存登录状态映射，实现 ticket 到 openid 的转换，允许系统在用户扫码后识别用户身份
    @Bean(name = "openidToken")
//    Cache<String, String>
//第一个String：键类型，这里是ticket（二维码票据）
//第二个String：值类型，这里是openid（用户标识）
    public Cache<String, String> openidToken() {
        return CacheBuilder.newBuilder()
                .expireAfterWrite(1, TimeUnit.HOURS)//设置缓存条目在写入后1小时过期，比access_token的过期时间短，可能是因为登录票据的安全性要求更高
                .build();
    }

    @Bean
    public EventBus eventBusListener(OrderPaySuccessListener listener){
        EventBus eventBus = new EventBus();
        eventBus.register(listener);
        return eventBus;
    }

}
