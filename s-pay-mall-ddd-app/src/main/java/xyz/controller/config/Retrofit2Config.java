package xyz.controller.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;
import xyz.controller.infrastructure.gateway.IWeixinApiService;

/**
 * <AUTHOR> https://github.com/World-controller @GoAndRun
 * @description
 * @create 2025/8/3 8:15
 */
@Slf4j
@Configuration
public class Retrofit2Config {

    private static final String BASE_URL = "https://api.weixin.qq.com/";//字符串字面量，微信API的基础URL

    @Bean
//    Retrofit：返回类型，这是Retrofit库的核心类，Retrofit是Square公司开发的HTTP客户端库，它可以将HTTP API转换为Java接口
    public Retrofit retrofit() {
        return new Retrofit.Builder()
                .baseUrl(BASE_URL)//设置所有HTTP请求的基础URL为"https://api.weixin.qq.com/"，后续的API调用会在这个基础URL上拼接具体的路径
                .addConverterFactory(JacksonConverterFactory.create()).build();//配置JSON转换器并构建最终的Retrofit实例
//        JacksonConverterFactory.create()：创建Jackson转换工厂
//Jackson是一个流行的JSON处理库
//这个工厂负责将Java对象转换为JSON，以及将JSON转换为Java对象
//当发送HTTP请求时，Java对象会被转换为JSON格式
//当接收HTTP响应时，JSON会被转换为Java对象
    }


    //使用Retrofit的create方法生成API服务（IWeixinApiService）实现，返回可以直接调用微信API的服务对象（IWeixinApiService的实现类）
    @Bean
    public IWeixinApiService weixinApiService(Retrofit retrofit) {
        return retrofit.create(IWeixinApiService.class);//Retrofit会分析 IWeixinApiService接口中的注解，自动生成HTTP请求代码
        // IWeixinApiService.class告诉Retrofit要为哪个接口创建实现
        // retrofit.create()：调用Retrofit对象的create方法，这是Retrofit的核心功能之一，它使用动态代理技术，根据接口定义自动生成实现类
    }


}
